package securitygroup

import (
	"context"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/securitygroup Interface

// Interface - 安全组相关接口
type Interface interface {
	GetSecurityGroup(ctx context.Context, vpcID, securityGroupID string) (*bcc.SecurityGroup, error)
	CreateCCEDefaultSecurityGroup(ctx context.Context, vpcID string) (string, error)

	// Node
	GetCCENodeSGRulesExcluded(ctx context.Context, vpcID, securityGroupID string) ([]bcc.SecurityGroupRule, error)
	CheckCCENodeSGRulesAllIncluded(ctx context.Context, vpcID, securityGroupID string) (bool, error)
	AddCCENodeSGRulesExcluded(ctx context.Context, vpcID, securityGroupID string) error

	// 自定义 Master
	GetCCEMasterSGRulesExcluded(ctx context.Context, vpcID, securityGroupID string) ([]bcc.SecurityGroupRule, error)
	CheckCCEMasterSGRulesAllIncluded(ctx context.Context, vpcID, securityGroupID string) (bool, error)
	AddCCEMasterSGRulesExcluded(ctx context.Context, vpcID, securityGroupID string) error

	// Serverless
	EnsureServerlessMasterSecurityGroup(ctx context.Context, vpcID string) (string, error)

	// CIDR 管理（批量接口）
	CheckSecurityGroupCIDRs(ctx context.Context, vpcID string, cidrs []string, securityGroups ccesdk.SecurityGroupConfig) ([]ccesdk.CIDRCheckResult, error)
	CheckSecurityGroupCIDRsAllExist(ctx context.Context, vpcID string, cidrs []string, securityGroups ccesdk.SecurityGroupConfig) (bool, error)
	AddSecurityGroupCIDRs(ctx context.Context, vpcID string, cidrs []ccesdk.CIDRSubnetPair, securityGroups ccesdk.SecurityGroupConfig) (bool, []ccesdk.CIDRProcessResult, error)
}
